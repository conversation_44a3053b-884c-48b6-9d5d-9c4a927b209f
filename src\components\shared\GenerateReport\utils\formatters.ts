/**
 * Convert numbers to Arabic numerals
 */
export const convertToArabicNumerals = (num: number | string | undefined | null): string => {
  if (num === undefined || num === null) return "";
  const numStr = Math.floor(Number(num)).toString();
  return numStr.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
};

/**
 * Format currency in Arabic
 */
export const formatArabicCurrency = (
  amount: number | string | undefined | null, 
  currency: string = "جنيه"
): string => {
  if (amount === undefined || amount === null) return "";
  const arabicAmount = convertToArabicNumerals(amount);
  return `${arabicAmount} ${currency}`;
};

/**
 * Format dates properly in Arabic using dd/mm/yyyy format
 */
export const formatArabicDate = (dateStr: string): string => {
  console.log(`📅 formatArabicDate input: "${dateStr}"`);

  const date = new Date(dateStr);
  console.log(`📅 Parsed date object:`, date);

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString();

  console.log(`📅 Date components: day=${day}, month=${month}, year=${year}`);

  // Convert to Arabic numerals
  const arabicDay = convertToArabicNumerals(day);
  const arabicMonth = convertToArabicNumerals(month);
  const arabicYear = convertToArabicNumerals(year);

  const result = `${arabicDay}/${arabicMonth}/${arabicYear}`;
  console.log(`📅 Final Arabic date: "${result}"`);

  return result;
};

/**
 * Format dates for English using dd/mm/yyyy format (consistent with Arabic)
 */
export const formatEnglishDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString();
  
  return `${day}/${month}/${year}`;
};

/**
 * Clean and format Arabic text properly while preserving English words
 */
export const formatArabicText = (text: string): string => {
  if (!text) return '';
  // Only remove special characters but preserve Arabic, English letters, numbers, and common punctuation
  return text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\,\-\+\%\$\€\£\/\:a-zA-Z]/g, '').trim();
};

/**
 * Smart date formatting that works for both languages with consistent dd/mm/yyyy format
 */
export const formatDateForPDF = (dateStr: string, language: string): string => {
  if (!dateStr) {
    console.warn('⚠️ Empty date string provided to formatDateForPDF');
    return '';
  }

  console.log(`📅 Formatting date: "${dateStr}" for language: ${language}`);

  try {
    let formattedDate: string;
    if (language === 'ar') {
      formattedDate = formatArabicDate(dateStr);
      console.log(`📅 Arabic date formatted: "${dateStr}" -> "${formattedDate}"`);
    } else {
      formattedDate = formatEnglishDate(dateStr);
      console.log(`📅 English date formatted: "${dateStr}" -> "${formattedDate}"`);
    }
    return formattedDate;
  } catch (error) {
    console.error('❌ Date formatting error:', error);
    return dateStr; // Return original if formatting fails
  }
};

/**
 * Smart content display - preserves ALL text while applying language-specific number formatting
 * Enhanced to handle mixed Arabic/English content properly in PDFs
 */
export const formatMixedContent = (text: string, language: string, type: 'text' | 'currency' | 'date' = 'text'): string => {
  if (!text) return '';

  console.log(`🔤 formatMixedContent: "${text}" (${type}, ${language})`);

  if (type === 'currency') {
    const amount = Number(text);
    if (!isNaN(amount)) {
      const result = language === 'ar' ? formatArabicCurrency(amount) : Math.round(amount).toString();
      console.log(`💰 Currency formatted: "${text}" -> "${result}"`);
      return result;
    }
  }

  if (type === 'date') {
    const result = formatDateForPDF(text, language);
    console.log(`📅 Date formatted: "${text}" -> "${result}"`);
    return result;
  }

  // For regular text, ensure proper encoding for PDF rendering
  let processedText = text;

  // Normalize the text to ensure proper Unicode encoding
  try {
    processedText = text.normalize('NFC');
    console.log(`🔤 Text normalized: "${text}" -> "${processedText}"`);
  } catch (error) {
    console.warn('⚠️ Text normalization failed:', error);
    processedText = text;
  }

  // Only apply Arabic numeral conversion if language is Arabic AND text contains numbers
  if (language === 'ar' && /\d/.test(processedText)) {
    // Convert only the numbers to Arabic numerals, preserve all text
    processedText = processedText.replace(/\d/g, (digit) => "٠١٢٣٤٥٦٧٨٩"[parseInt(digit)]);
    console.log(`🔢 Arabic numerals applied: "${text}" -> "${processedText}"`);
  }

  console.log(`✅ Final mixed content: "${processedText}"`);
  return processedText; // Return processed text
};

/**
 * Detect if text contains Arabic characters
 */
export const containsArabic = (text: string): boolean => {
  if (!text) return false;
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};
