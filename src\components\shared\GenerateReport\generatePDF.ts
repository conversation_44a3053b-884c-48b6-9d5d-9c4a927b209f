import jsPDF from "jspdf";
import { PDFData, PDFOptions, PDFMode } from "./types";
import { loadLogo, loadPerfectFont, renderText } from "./utils/fontHelpers";
import { getTranslation } from "./utils/translations";
import { formatDateForPDF } from "./utils/formatters";
import { dataContainsArabic } from "./utils/dataHelpers";
import { generateEventsTable } from "./utils/eventsTable";
import { generateLocationsTable } from "./utils/locationsTable";
import { generateContactsTable } from "./utils/contactsTable";
import { generateReservationsTable } from "./utils/reservationsTable";
import { generateContractsTable } from "./utils/contractsTable";

/**
 * Main PDF generation function
 */
export const generateCustomPDF = async (
  data: PDFData,
  options: PDFOptions,
  mode: PDFMode
): Promise<void> => {
  const { language, includeCompanyInfo, includeLogo, includeDate, customTitle, fields } = options;

  // RESPECT USER'S LANGUAGE CHOICE: Use the selected language from the modal
  const effectiveLanguage = language;
  
  console.log('📋 PDF Generation Settings:', {
    selectedLanguage: language,
    effectiveLanguage,
    includeCompanyInfo,
    includeLogo,
    includeDate,
    customTitle,
    mode,
    dataKeys: Object.keys(data),
    eventCount: data.events?.length || 0
  });

  try {
    // Create PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    let currentY = 20;

    // Load the PERFECT font for Arabic + English
    const fontInfo = await loadPerfectFont(doc);
    console.log('🔤 Font info loaded:', fontInfo);

    // Set the font explicitly for titles and text - with better error handling
    try {
      doc.setFont(fontInfo.fontName, 'normal');
      console.log('✅ Font set successfully for titles:', fontInfo.fontName);

      // Test if the font actually works by trying to get text width
      doc.getTextWidth('Test');
      console.log('✅ Font tested and working:', fontInfo.fontName);
    } catch (error) {
      console.error('❌ Error setting or testing font:', error);
      // Update fontInfo to reflect the actual working font
      fontInfo.fontName = 'helvetica';
      fontInfo.hasBold = true;
      doc.setFont('helvetica', 'normal');
      console.log('⚠️ Updated fontInfo to use helvetica fallback');
    }

    // Log font and language information for debugging
    console.log('📋 PDF Generation Debug Info:', {
      language: effectiveLanguage,
      fontName: fontInfo.fontName,
      hasBold: fontInfo.hasBold,
      autoTableFont: 'helvetica' // autoTable will use helvetica for compatibility
    });

    // Load and add company logo
    if (includeLogo) {
      try {
        const logoBase64 = await loadLogo('/images/logo/saray_vera_white.png');
        if (logoBase64) {
          const logoWidth = 40;
          const logoHeight = 20;
          const logoX = effectiveLanguage === "ar" ? pageWidth - logoWidth - 14 : 14;
          doc.addImage(`data:image/png;base64,${logoBase64}`, 'PNG', logoX, currentY, logoWidth, logoHeight);
        }
      } catch (logoError) {
        console.warn("Could not load logo, continuing without it:", logoError);
      }
    }

    // Company name and document info
    if (includeCompanyInfo) {
      const companyName = getTranslation("company_name", effectiveLanguage);
      console.log('🏢 Rendering company name:', companyName, 'Language:', effectiveLanguage);

      // For English PDFs, always use helvetica for company name to ensure visibility
      const companyFont = effectiveLanguage === 'en' ? 'helvetica' : fontInfo.fontName;
      console.log('🏢 Company font selected:', companyFont);

      // Set font before getting text width
      try {
        doc.setFont(companyFont, 'normal');
        doc.setFontSize(16);
      } catch (error) {
        console.warn('⚠️ Company font failed, using helvetica:', error);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(16);
      }

      const companyNameWidth = doc.getTextWidth(companyName);
      const companyNameX = effectiveLanguage === "ar" ? 14 : pageWidth - companyNameWidth - 14;
      renderText(doc, companyName, companyNameX, currentY + 8, {
        fontSize: 16,
        fontName: companyFont
      });
    }

    // Document creation date
    if (includeDate) {
      const currentDate = new Date();
      const formattedDate = formatDateForPDF(currentDate.toISOString(), effectiveLanguage);
      const createdLabel = getTranslation("created", effectiveLanguage);
      const dateText = effectiveLanguage === "ar"
        ? `${formattedDate} :${createdLabel}`
        : `${createdLabel}: ${formattedDate}`;
      console.log('📅 Rendering date text:', dateText, 'Language:', effectiveLanguage);

      // For English PDFs, always use helvetica for date to ensure visibility
      const dateFont = effectiveLanguage === 'en' ? 'helvetica' : fontInfo.fontName;
      console.log('📅 Date font selected:', dateFont);

      // Set font before getting text width
      try {
        doc.setFont(dateFont, 'normal');
        doc.setFontSize(10);
      } catch (error) {
        console.warn('⚠️ Date font failed, using helvetica:', error);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(10);
      }

      const dateWidth = doc.getTextWidth(dateText);
      const dateX = effectiveLanguage === "ar" ? 14 : pageWidth - dateWidth - 14;
      renderText(doc, dateText, dateX, currentY + 18, {
        fontSize: 10,
        fontName: dateFont
      });
    }

    // Add a separator line (EXACT original)
    currentY += 30;
    doc.setLineWidth(0.5);
    doc.line(14, currentY, pageWidth - 14, currentY);
    currentY += 10;

    // Report Title - Professional styling
    const reportTitle = customTitle;
    console.log('📋 Rendering document title:', reportTitle, 'Language:', effectiveLanguage);

    // For Arabic PDFs, test if the Arabic font actually works for titles
    let titleFont = effectiveLanguage === 'en' ? 'helvetica' : fontInfo.fontName;
    console.log('📋 Initial title font selected:', titleFont);

    // Set font and test if it works
    let fontWorking = false;
    try {
      doc.setFont(titleFont, 'normal');
      doc.setFontSize(20);
      // Test if the font works by trying to get text width
      doc.getTextWidth(reportTitle);
      fontWorking = true;
      console.log('✅ Title font test passed:', titleFont);
    } catch (error) {
      console.warn('⚠️ Title font failed test:', titleFont, error);
      fontWorking = false;
    }

    // If Arabic font failed, fallback to helvetica even for Arabic PDFs
    if (!fontWorking) {
      titleFont = 'helvetica';
      console.log('📋 Falling back to helvetica for title');
      try {
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(20);
        console.log('✅ Helvetica fallback set for title');
      } catch (fallbackError) {
        console.error('❌ Even helvetica failed for title:', fallbackError);
      }
    }

    const reportTitleWidth = doc.getTextWidth(reportTitle);
    const titleX = (pageWidth - reportTitleWidth) / 2; // Center the title

    console.log('📋 Rendering title with font:', titleFont, 'at position:', titleX, currentY);
    renderText(doc, reportTitle, titleX, currentY, {
      align: 'center',
      fontSize: 20,
      fontName: titleFont
    });

    // Add underline to title (EXACT original)
    doc.setLineWidth(0.3);
    doc.line(titleX, currentY + 2, titleX + reportTitleWidth, currentY + 2);
    currentY += 15;

    // Reset font to the perfect font
    doc.setFont(fontInfo.fontName, 'normal');

    // Generate tables based on mode
    if (mode === 'events' && data.events) {
      await generateEventsTable(doc, data.events, effectiveLanguage, currentY, pageWidth, fields, fontInfo);
    } else if (mode === 'locations' && data.locations) {
      await generateLocationsTable(doc, data.locations, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'reservations' && data.reservations) {
      await generateReservationsTable(doc, data.reservations, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'contracts' && data.contracts) {
      await generateContractsTable(doc, data.contracts, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'contacts' && data.contacts) {
      await generateContactsTable(doc, data.contacts, effectiveLanguage, currentY, pageWidth, fontInfo);
    }

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${customTitle.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_${timestamp}.pdf`;

    // Save the PDF
    doc.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};
