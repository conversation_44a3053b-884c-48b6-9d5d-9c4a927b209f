import React from 'react';
import { generateCustomPDF } from '../generatePDF';
import { PDFData, PDFOptions, PDFMode } from '../types';

/**
 * Debug component to test PDF generation with minimal data
 */
const PDFDebugger: React.FC = () => {
  const testEnglishPDF = async () => {
    console.log('🧪 Testing English PDF generation...');
    
    const testData: PDFData = {
      events: [
        {
          id: '1',
          title: 'Test English Event',
          amount: 1000,
          dueDate: '2024-01-15',
          status: 'pending' as any,
          priority: 'high' as any,
          description: 'This is a test event in English',
          category: 'income' as any,
          type_id: '1',
          contact_id: '1',
          location_id: '1'
        }
      ]
    };

    const options: PDFOptions = {
      language: 'en',
      includeCompanyInfo: true,
      includeLogo: true,
      includeDate: true,
      customTitle: 'English Test Report',
      fields: []
    };

    try {
      await generateCustomPDF(testData, options, 'events');
      console.log('✅ English PDF generated successfully');
    } catch (error) {
      console.error('❌ English PDF generation failed:', error);
    }
  };

  const testArabicPDF = async () => {
    console.log('🧪 Testing Arabic PDF generation...');
    
    const testData: PDFData = {
      events: [
        {
          id: '1',
          title: 'حدث تجريبي عربي',
          amount: 1500,
          dueDate: '2024-01-20',
          status: 'completed' as any,
          priority: 'medium' as any,
          description: 'هذا حدث تجريبي باللغة العربية',
          category: 'expense' as any,
          type_id: '1',
          contact_id: '1',
          location_id: '1'
        }
      ]
    };

    const options: PDFOptions = {
      language: 'ar',
      includeCompanyInfo: true,
      includeLogo: true,
      includeDate: true,
      customTitle: 'تقرير تجريبي عربي',
      fields: []
    };

    try {
      await generateCustomPDF(testData, options, 'events');
      console.log('✅ Arabic PDF generated successfully');
    } catch (error) {
      console.error('❌ Arabic PDF generation failed:', error);
    }
  };

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="text-lg font-bold mb-4">PDF Debug Tools</h3>
      <div className="space-x-4">
        <button
          onClick={testEnglishPDF}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test English PDF
        </button>
        <button
          onClick={testArabicPDF}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test Arabic PDF
        </button>
      </div>
      <p className="text-sm text-gray-600 mt-2">
        Check browser console for detailed logs
      </p>
    </div>
  );
};

export default PDFDebugger;
