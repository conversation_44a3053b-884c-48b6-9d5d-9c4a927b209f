import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { Contact } from "@/lib/types/contacts";
import { FontInfo } from "../types";
import { getTranslation } from "../utils/translations";
import { getAutoTableFont } from "../utils/fontHelpers";

/**
 * Generate Contacts Table
 */
export const generateContactsTable = async (
  doc: jsPDF,
  contacts: Contact[],
  language: string,
  startY: number,
  pageWidth: number,
  fontInfo?: FontInfo
): Promise<number> => {
  let currentY = startY;

  const t = (key: string) => getTranslation(key, language);

  const tableColumn = language === "ar"
    ? [t("name"), t("email"), t("phone"), t("company")]
    : [t("name"), t("email"), t("phone"), t("company")];

  const contactRows = contacts.map(contact => [
    contact.name || "-",
    contact.email || "-",
    contact.phone || "-",
    contact.company || "-"
  ]);

  doc.setFontSize(14);
  const contactsTitle = t("contacts");
  const titleWidth = doc.getTextWidth(contactsTitle);
  doc.text(contactsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 5;

  autoTable(doc, {
    head: [tableColumn],
    body: contactRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontStyle: getAutoTableFont(fontInfo, language) === 'helvetica' ? 'bold' : 'normal', // Only use bold with helvetica
      halign: language === "ar" ? "right" : "left",
      fillColor: [52, 152, 219],
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    }
  });

  return (doc as any).lastAutoTable.finalY + 10;
};
