import jsPDF from "jspdf";
import { FontInfo, RenderTextOptions } from "../types";

/**
 * Load logo from URL and return base64 string
 */
export const loadLogo = async (logoUrl: string): Promise<string | null> => {
  try {
    const response = await fetch(logoUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch logo: ${response.status}`);
    }
    
    const logoData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(logoData);
    
    // Validate PNG header
    if (uint8Array.length < 8 || 
        uint8Array[0] !== 0x89 || uint8Array[1] !== 0x50 || 
        uint8Array[2] !== 0x4E || uint8Array[3] !== 0x47) {
      throw new Error("Invalid PNG file format");
    }
    
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading logo:", error);
    return null;
  }
};

/**
 * Load font and return base64 string
 */
export const loadFont = async (fontUrl: string): Promise<string> => {
  try {
    const response = await fetch(fontUrl);
    const fontData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(fontData);
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading font:", error);
    return "";
  }
};

/**
 * Font loading system for Arabic + English with proper fallback
 * IMPORTANT: Always test if fonts actually work before returning them
 */
export const loadPerfectFont = async (doc: jsPDF): Promise<FontInfo> => {
  // Test function to verify if a font actually works
  const testFont = (fontName: string): boolean => {
    try {
      doc.setFont(fontName, 'normal');
      // Try to get text width - this will fail if font doesn't work
      doc.getTextWidth('Test');
      return true;
    } catch (error) {
      console.warn(`❌ Font ${fontName} failed test:`, error);
      return false;
    }
  };

  try {
    // Try to load Noto Sans Arabic first (BEST for Arabic + English mixed content)
    const regularResponse = await fetch('/NotoSansArabic-Regular.ttf');
    if (regularResponse.ok) {
      const fontData = await regularResponse.arrayBuffer();
      const uint8Array = new Uint8Array(fontData);
      let binary = "";
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const fontBase64 = btoa(binary);

      doc.addFileToVFS('NotoSansArabic-Regular.ttf', fontBase64);
      doc.addFont('NotoSansArabic-Regular.ttf', 'NotoSansArabic', 'normal');

      // TEST if the font actually works
      if (testFont('NotoSansArabic')) {
        console.log('✅ Noto Sans Arabic font loaded and tested successfully!');
        return { fontName: 'NotoSansArabic', hasBold: false };
      } else {
        console.warn('⚠️ Noto Sans Arabic loaded but failed test, trying Amiri');
      }
    }
  } catch (error) {
    console.warn('⚠️ Noto Sans Arabic failed to load, trying Amiri:', error);
  }

  try {
    // Fallback to Amiri font
    const response = await fetch('/Amiri-Regular.ttf');
    if (response.ok) {
      const fontData = await response.arrayBuffer();
      const uint8Array = new Uint8Array(fontData);
      let binary = "";
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const fontBase64 = btoa(binary);

      doc.addFileToVFS('Amiri-Regular.ttf', fontBase64);
      doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');

      // TEST if the font actually works
      if (testFont('Amiri')) {
        console.log('✅ Amiri font loaded and tested successfully!');
        return { fontName: 'Amiri', hasBold: false };
      } else {
        console.warn('⚠️ Amiri loaded but failed test, using helvetica');
      }
    }
  } catch (error) {
    console.warn('⚠️ Amiri font also failed to load, using system fonts:', error);
  }

  // Final fallback to system fonts - ALWAYS test helvetica too
  if (testFont('helvetica')) {
    console.log('✅ Using Helvetica as final fallback - tested and working');
    return { fontName: 'helvetica', hasBold: true };
  } else {
    console.error('❌ Even helvetica failed! Using times as last resort');
    return { fontName: 'times', hasBold: true };
  }
};

/**
 * Get font name that works with autoTable - ALWAYS use helvetica for compatibility
 * Arabic fonts don't work reliably with autoTable, so we use helvetica for all table content
 */
export const getAutoTableFont = (fontInfo?: FontInfo, language?: string): string => {
  console.log('🔤 getAutoTableFont called with:', {
    fontInfoName: fontInfo?.fontName,
    language,
    willUse: 'helvetica',
    reason: 'Always using helvetica for autoTable compatibility - Arabic fonts cause rendering issues'
  });

  // ALWAYS use helvetica for autoTable - it's the most reliable font for table content
  // Arabic fonts cause text rendering issues in autoTable
  return 'helvetica';
};

/**
 * Text rendering for Arabic + English with improved font handling
 */
export const renderText = (
  doc: jsPDF,
  text: string,
  x: number,
  y: number,
  options: RenderTextOptions = {}
): void => {
  const { align = 'left', fontSize, fontName } = options;

  if (fontSize) {
    doc.setFontSize(fontSize);
  }

  // ALWAYS ensure we have a working font
  let workingFont = 'helvetica'; // Safe default

  if (fontName) {
    try {
      doc.setFont(fontName, 'normal');
      // Test if the font works by trying to get text width
      doc.getTextWidth('Test');
      workingFont = fontName;
      console.log(`✅ Font set and tested successfully: ${fontName}`);
    } catch (error) {
      console.warn(`⚠️ Font ${fontName} failed, using helvetica:`, error);
      doc.setFont('helvetica', 'normal');
      workingFont = 'helvetica';
    }
  } else {
    // Default to helvetica if no font specified
    doc.setFont('helvetica', 'normal');
  }

  // Clean and prepare text - ensure it's not empty
  let finalText = text || '';
  if (!finalText.trim()) {
    console.warn('⚠️ Empty text provided to renderText');
    return;
  }

  // Render text with proper alignment
  try {
    doc.text(finalText, x, y, { align: align as any });
    console.log(`✅ Text rendered with ${workingFont}: "${finalText.substring(0, 50)}..." at (${x}, ${y})`);
  } catch (error) {
    console.warn('❌ Text rendering error with alignment:', error);
    // Fallback rendering without alignment
    try {
      doc.text(finalText, x, y);
      console.log(`✅ Text rendered (no alignment) with ${workingFont}: "${finalText.substring(0, 50)}..." at (${x}, ${y})`);
    } catch (fallbackError) {
      console.error('❌ Complete text rendering failure:', fallbackError);
      // Last resort - try with simple text
      try {
        doc.setFont('helvetica', 'normal');
        doc.text(String(finalText), x, y);
        console.log(`✅ Text rendered (last resort): "${finalText.substring(0, 50)}..."`);
      } catch (lastError) {
        console.error('❌ Even last resort failed:', lastError);
      }
    }
  }
};

/**
 * Get appropriate font for tables - use the best available font
 */
export const getTableFont = (fontInfo?: FontInfo): string => {
  if (fontInfo?.fontName) {
    return fontInfo.fontName;
  }
  return 'helvetica'; // Safe fallback that always works
};

/**
 * Get appropriate font style for headers
 */
export const getHeaderFontStyle = (fontInfo?: FontInfo): string | undefined => {
  return fontInfo?.hasBold ? 'bold' : undefined;
};

/**
 * Fallback font system - use this for all hardcoded font references
 */
export const getFallbackFont = (): string => {
  return 'helvetica'; // Safe fallback that always has bold support
};
