import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { EventDetails } from "@/lib/interfaces/finaces";
import { FontInfo, PDFField } from "../types";
import { getTranslation } from "../utils/translations";
import { formatArabicCurrency, formatDateForPDF, formatMixedContent } from "../utils/formatters";
import { renderText, getAutoTableFont } from "../utils/fontHelpers";

/**
 * Generate Events Table with field editing support
 */
export const generateEventsTable = async (
  doc: jsPDF,
  events: EventDetails[],
  language: string,
  startY: number,
  pageWidth: number,
  fields: PDFField[],
  fontInfo: FontInfo
): Promise<number> => {
  let currentY = startY;

  // Apply field edits if provided
  let processedEvents = [...events];
  if (fields && fields.length > 0) {
    processedEvents = events.map((event, index) => {
      const eventFields = fields.filter(f => f.key.startsWith(`event_${index}_`));
      let updatedEvent = { ...event };

      eventFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedEvent.title = field.value;
        if (field.key.endsWith('_amount')) updatedEvent.amount = parseFloat(field.value) || 0;
        if (field.key.endsWith('_dueDate')) updatedEvent.dueDate = field.value;
        if (field.key.endsWith('_status')) updatedEvent.status = field.value as any;
        if (field.key.endsWith('_priority')) updatedEvent.priority = field.value as any;
        if (field.key.endsWith('_description')) updatedEvent.description = field.value;
      });

      return updatedEvent;
    });
  }

  // Use the comprehensive translation function
  const t = (key: string) => {
    const translation = getTranslation(key, language);
    console.log(`🌐 Translation: "${key}" -> "${translation}" (${language})`);
    return translation;
  };

  // Table columns with proper translations
  const tableColumn = language === "ar"
    ? [t("title"), t("dueDate"), t("amount"), t("status")]
    : [t("title"), t("dueDate"), t("amount"), t("status")];

  console.log('📊 Table columns:', tableColumn, 'Language:', language);
  console.log('🔤 Font info for table:', fontInfo);
  console.log('🔤 AutoTable font will be:', getAutoTableFont(fontInfo, language));

  const incomeRows: any[] = [];
  const expenseRows: any[] = [];

  // Process events with MIXED CONTENT SUPPORT
  processedEvents.forEach(event => {
    console.log('📝 Processing event:', event.title, 'Category:', event.category);
    // Use consistent date formatting for both languages (dd/mm/yyyy)
    const formattedDate = formatDateForPDF(event.dueDate, language);
    
    // Use smart currency formatting
    const formattedAmount = formatMixedContent(
      (Math.round(Number(event.amount) || 0)).toString(), 
      language, 
      'currency'
    );

    const eventData = [
      event.title, // Always preserve original title as-is, don't format it
      formattedDate,
      formattedAmount,
      t(event.status) // Translate status
    ];
    
    console.log('📊 Event data row:', eventData, 'Original title:', event.title);
    
    if (event.category === "income") incomeRows.push(eventData);
    else if (event.category === "expense") expenseRows.push(eventData);
  });

  // EXACT original totals calculation
  const totalIncome = Math.round(processedEvents
    .filter(e => e.category === "income")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalExpenses = Math.round(processedEvents
    .filter(e => e.category === "expense")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalIncomeText = language === "ar"
    ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
    : `${t("total income")}: ${totalIncome}`;
  const totalExpensesText = language === "ar"
    ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
    : `${t("total expenses")}: ${totalExpenses}`;

  // Income Table
  if (incomeRows.length > 0) {
    console.log('💰 Generating income table with', incomeRows.length, 'rows');
    console.log('💰 Income rows data:', incomeRows);
    
    const incomeTitle = t("incomeEvents");
    console.log('💰 Rendering income title:', incomeTitle, 'Language:', language);

    // For English PDFs, always use helvetica for table titles to ensure visibility
    const titleFont = language === 'en' ? 'helvetica' : fontInfo.fontName;
    console.log('💰 Income title font selected:', titleFont);

    // Set font before getting text width
    try {
      doc.setFont(titleFont, 'normal');
      doc.setFontSize(14);
    } catch (error) {
      console.warn('⚠️ Income title font failed, using helvetica:', error);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(14);
    }

    const titleWidth = doc.getTextWidth(incomeTitle);
    const titleX = language === "ar" ? pageWidth - titleWidth - 14 : 14;

    // Render title with proper font settings
    renderText(doc, incomeTitle, titleX, currentY, {
      fontSize: 14,
      fontName: titleFont
    });
    currentY += 8; // Reduced spacing to keep title close to table

    let autoTableFont = getAutoTableFont(fontInfo, language);
    console.log('💰 Income table configuration:', {
      headers: tableColumn,
      rowCount: incomeRows.length,
      font: autoTableFont,
      language: language,
      sampleRow: incomeRows[0]
    });

    // Test if the selected font actually works with autoTable
    if (autoTableFont !== 'helvetica') {
      try {
        doc.setFont(autoTableFont, 'normal');
        doc.getTextWidth('Test Arabic: اختبار');
        console.log('✅ Arabic font test passed for income table:', autoTableFont);
      } catch (error) {
        console.warn('⚠️ Arabic font failed for income table:', autoTableFont, error);
        console.log('🔄 Falling back to helvetica for income table');
        autoTableFont = 'helvetica';
      }
    }

    autoTable(doc, {
      head: [tableColumn],
      body: incomeRows,
      startY: currentY + 3, // Reduced gap between title and table
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: autoTableFont, // Use autoTable-compatible font
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
        overflow: 'linebreak', // Handle long text properly
        cellWidth: 'wrap', // Allow cells to wrap content
        // Add text rendering options for better Unicode support
        textColor: [0, 0, 0], // Ensure text color is set
        fillColor: [255, 255, 255] // Ensure background is set
      },
      headStyles: {
        font: autoTableFont, // Use the tested font
        fontStyle: 'bold', // Always use bold for headers
        halign: language === "ar" ? "right" : "left",
        fillColor: [41, 128, 185], // EXACT original blue
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left", cellWidth: 'auto' }, // Title column - auto width
        1: { halign: language === "ar" ? "right" : "center", cellWidth: 40 }, // Date column - increased width for full date
        2: { halign: language === "ar" ? "right" : "right", cellWidth: 30 }, // Amount column - increased width
        3: { halign: language === "ar" ? "right" : "center", cellWidth: 30 } // Status column - increased width
      },
      // Add callback to handle text rendering issues
      didParseCell: function(data: any) {
        // Ensure text is properly encoded
        if (data.cell.text && Array.isArray(data.cell.text)) {
          data.cell.text = data.cell.text.map((text: string) => {
            if (typeof text === 'string') {
              // Normalize Unicode text for better rendering
              return text.normalize('NFC');
            }
            return text;
          });
        }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;

    // For English PDFs, always use helvetica for total text to ensure visibility
    const totalFont = language === 'en' ? 'helvetica' : fontInfo.fontName;
    console.log('💰 Income total font selected:', totalFont);

    // Set font before getting text width
    try {
      doc.setFont(totalFont, 'normal');
      doc.setFontSize(12);
    } catch (error) {
      console.warn('⚠️ Income total font failed, using helvetica:', error);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(12);
    }

    const incomeTextWidth = doc.getTextWidth(totalIncomeText);
    const incomeTextX = language === "ar" ? pageWidth - incomeTextWidth - 14 : 14;
    renderText(doc, totalIncomeText, incomeTextX, currentY, {
      fontSize: 12,
      fontName: totalFont
    });
  }

  // Expense Table
  if (expenseRows.length > 0) {
    currentY += 15;
    const expenseTitle = t("expenseEvents");
    console.log('💸 Rendering expense title:', expenseTitle, 'Language:', language);

    // For English PDFs, always use helvetica for table titles to ensure visibility
    const expenseTitleFont = language === 'en' ? 'helvetica' : fontInfo.fontName;
    console.log('💸 Expense title font selected:', expenseTitleFont);

    // Set font before getting text width
    try {
      doc.setFont(expenseTitleFont, 'normal');
      doc.setFontSize(14);
    } catch (error) {
      console.warn('⚠️ Expense title font failed, using helvetica:', error);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(14);
    }

    const titleWidth = doc.getTextWidth(expenseTitle);
    const titleX = language === "ar" ? pageWidth - titleWidth - 14 : 14;

    renderText(doc, expenseTitle, titleX, currentY, {
      fontSize: 14,
      fontName: expenseTitleFont
    });
    currentY += 8; // Reduced spacing to keep title close to table

    let expenseAutoTableFont = getAutoTableFont(fontInfo, language);
    console.log('💸 Expense table configuration:', {
      headers: tableColumn,
      rowCount: expenseRows.length,
      font: expenseAutoTableFont,
      language: language,
      sampleRow: expenseRows[0]
    });

    // Test if the selected font actually works with autoTable
    if (expenseAutoTableFont !== 'helvetica') {
      try {
        doc.setFont(expenseAutoTableFont, 'normal');
        doc.getTextWidth('Test Arabic: اختبار');
        console.log('✅ Arabic font test passed for expense table:', expenseAutoTableFont);
      } catch (error) {
        console.warn('⚠️ Arabic font failed for expense table:', expenseAutoTableFont, error);
        console.log('🔄 Falling back to helvetica for expense table');
        expenseAutoTableFont = 'helvetica';
      }
    }

    autoTable(doc, {
      head: [tableColumn],
      body: expenseRows,
      startY: currentY + 3, // Reduced gap between title and table
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: expenseAutoTableFont, // Use autoTable-compatible font
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
        overflow: 'linebreak', // Handle long text properly
        cellWidth: 'wrap', // Allow cells to wrap content
        // Add text rendering options for better Unicode support
        textColor: [0, 0, 0], // Ensure text color is set
        fillColor: [255, 255, 255] // Ensure background is set
      },
      headStyles: {
        font: expenseAutoTableFont, // Use the tested font
        fontStyle: 'bold', // Always use bold for headers
        halign: language === "ar" ? "right" : "left",
        fillColor: [231, 76, 60], // EXACT original red
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left", cellWidth: 'auto' }, // Title column - auto width
        1: { halign: language === "ar" ? "right" : "center", cellWidth: 40 }, // Date column - increased width for full date
        2: { halign: language === "ar" ? "right" : "right", cellWidth: 30 }, // Amount column - increased width
        3: { halign: language === "ar" ? "right" : "center", cellWidth: 30 } // Status column - increased width
      },
      // Add callback to handle text rendering issues
      didParseCell: function(data: any) {
        // Ensure text is properly encoded
        if (data.cell.text && Array.isArray(data.cell.text)) {
          data.cell.text = data.cell.text.map((text: string) => {
            if (typeof text === 'string') {
              // Normalize Unicode text for better rendering
              return text.normalize('NFC');
            }
            return text;
          });
        }
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;

    // For English PDFs, always use helvetica for total text to ensure visibility
    const expenseTotalFont = language === 'en' ? 'helvetica' : fontInfo.fontName;
    console.log('💸 Expense total font selected:', expenseTotalFont);

    // Set font before getting text width
    try {
      doc.setFont(expenseTotalFont, 'normal');
      doc.setFontSize(12);
    } catch (error) {
      console.warn('⚠️ Expense total font failed, using helvetica:', error);
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(12);
    }

    const expenseTextWidth = doc.getTextWidth(totalExpensesText);
    const expenseTextX = language === "ar" ? pageWidth - expenseTextWidth - 14 : 14;
    renderText(doc, totalExpensesText, expenseTextX, currentY, {
      fontSize: 12,
      fontName: expenseTotalFont
    });
  }

  return currentY;
};
